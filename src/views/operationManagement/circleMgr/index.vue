<template>
  <AbsoluteContainer>
    <!-- 已删除 点击 回复评论弹窗 -->
    <el-dialog
      v-model="disabledShow"
      class="disabledAlert"
      size="default"
      border
      stripe
      highlight-current-row
      :show-close="false"
      :align-center="true"
      title="存在未撤销的上级评论，当前操作不被允许!"
    >
      <el-table :data="disableData" border stripe highlight-current-row>
        <el-table-column align="center" property="auto_pk_str" label="序号" width="150" />
        <el-table-column v-slot="scope" align="center" label="评论内容">
          <p v-html="scope.row.content"></p>
          <el-button style="float: right" type="primary" @click="copyAddress(scope.row.content)">
            复制
          </el-button>
        </el-table-column>
        <el-table-column v-slot="scope" align="center" label="评论内容">
          <p v-html="scope.row.content"></p>
        </el-table-column>
        <el-table-column align="center" property="comment_user_name" width="150" label="评论人" />
        <el-table-column v-slot="scope" align="center" width="150" label="操作">
          <el-button
            v-if="scope.$index === 0"
            style="padding-left: 0"
            text
            @click="deleteToPass ? handleDelPass(scope.row) : handleDelAdopt(scope.row)"
          >
            <span class="button_text">{{ deleteToPass ? '撤销并通过' : '撤销至待审' }}</span>
          </el-button>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            @click="
              disabledShow = false;
              commentIds = []
            "
            >关闭</el-button
          >
        </span>
      </template>
    </el-dialog>
    <div class="circleMain">
      <div class="cirle-list">
        <div class="head">
          <el-select
            v-model="left.circle_id"
            class="custom-select"
            placeholder="请选择圈子"
            style="width: 140px"
            clearable
            @change="selectChange"
          >
            <el-option
              v-for="item in circlelist"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
          <el-input
            v-model="left.article_title"
            placeholder="请输入关键词"
            style="width: 200px"
            class="input-with-select2"
            clearable
            @keyup.enter="handSearch"
          >
            <template #append>
              <el-button
                type="primary"
                :icon="Search"
                style="display: flex; align-items: center"
                @click="handSearch"
              >
              </el-button>
            </template>
          </el-input>
          <div class="tab">
            <div :class="left.comment_state === 1 ? 'child act' : 'child'" @click="tabChange(1)">
              全部
            </div>
            <div
              :class="
                left.comment_state === 0
                  ? leftShowTip
                    ? ' child act tip'
                    : 'child act'
                  : leftShowTip
                    ? ' child tip'
                    : 'child'
              "
              @click="tabChange(0)"
            >
              待审核
            </div>
          </div>
        </div>
        <div
          v-infinite-scroll="leftLoad"
          v-loading="leftLoading"
          :class="articleList.length ? 'list' : 'list null'"
          infinite-scroll-distance="600"
        >
          <div
            v-for="item in articleList"
            :key="item.id"
            :class="childId == item.id ? 'child act' : 'child'"
            @click="showDetail(item)"
          >
            <div class="left">
              <img v-if="item.list_pics[0]" :src="item.list_pics[0]" alt="" />
              <img v-else src="@/assets/images/circle/defaltImg.png" alt="" />
            </div>
            <div class="right">
              <div class="title">{{ item.list_title || '无标题' }}</div>
              <div class="footer">
                <div class="time">{{ item.published_at }}</div>
                <div class="nums">
                  <div class="commentNum">{{ item.passed_auditing_Count }}</div>
                  <div v-if="item.auditing_count > 0" class="line"></div>
                  <div v-if="item.auditing_count > 0" class="adoptNum">
                    {{ item.auditing_count }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="!articleList.length">
            <img src="@/assets/images/circle/noList.png" alt="" />
            <div class="font">暂无稿件</div>
          </div>
        </div>
      </div>
      <div v-if="isChildShow" class="right-box">
        <!-- 稿件详情 -->
        <Detail :detail="detail" />
        <!-- 评论内容 -->
        <div v-loading="loading" class="comment-box inCircle">
          <div class="tab">
            <div :class="commentChoise == 1 ? 'child act' : 'child'" @click="rightTabChange(1)">
              全部
            </div>
            <div :class="commentChoise == 2 ? 'child act' : 'child'" @click="rightTabChange(2)">
              <span v-if="comment.showTips" class="num">{{ comment.adoptNumber }}</span>
              待审核
            </div>
            <div :class="commentChoise == 3 ? 'child act' : 'child'" @click="rightTabChange(3)">
              已删除
            </div>
          </div>
          <div
            v-infinite-scroll="loadRight"
            class="content"
            infinite-scroll-distance="400"
            :infinite-scroll-disabled="infiniteDisabled"
          >
            <div
              v-if="commentChoise === 1 && (comment.hotList.length || comment.list.length)"
              class="comment-list"
            >
              <!-- 热评 -->
              <div v-for="(item, index) in comment.hotList" :key="item.id" class="child">
                <div class="head">
                  <div class="avatar">
                    <img :src="item.portrait_url" alt="" />
                  </div>
                  <div class="right">
                    <div class="name">
                      <SpammerMarker
                        v-model:spammer="item.spammer"
                        :disabled="commentChoise !== 2"
                        :user-data="{
                          user_name: item.comment_user_name,
                          user_id: item.account_id || item.comment_user_id
                        }"
                      >
                        <template #reference>
                          <span>{{ item.comment_user_name }}</span>
                        </template>
                      </SpammerMarker>
                    </div>
                    <el-tooltip
                      v-if="item.vest_mark"
                      class="box-item"
                      effect="dark"
                      :content="item.vest_admin_name"
                      placement="top"
                    >
                      <div class="guide">引导</div>
                    </el-tooltip>
                  </div>
                </div>
                <div class="comment-detail">
                  <!-- <span class="topping" v-if="item.top">置顶</span> -->
                  <span v-html="item.content"></span>
                  <img class="hot" src="@/assets/images/circle/hot.png" alt="" />
                  <!-- 敏感词 -->
                  <el-tooltip
                    v-if="item.sensitive_word"
                    class="box-item"
                    effect="light"
                    :content="item.sensitive_word"
                    placement="top"
                  >
                    <el-button
                      type="danger"
                      plain
                      size="small"
                      color="#F65520"
                      style="margin-left: 5px; padding: 0 3px; height: 18px"
                    >
                      敏
                    </el-button>
                  </el-tooltip>
                  <el-tooltip
                    v-if="item.delete_reason"
                    class="box-item"
                    effect="light"
                    :content="item.delete_reason"
                    placement="top"
                  >
                    <el-button
                      type="danger"
                      plain
                      size="small"
                      color="#FF8920"
                      style="margin-left: 5px; padding: 0 3px; height: 18px"
                    >
                      治
                    </el-button>
                  </el-tooltip>
                  <br v-if="item.content && (item.pic_url || item.expression_url)" />
                  <!-- 图片评论 -->
                  <el-popover v-if="item.pic_url" trigger="hover" placement="top" width="auto">
                    <div style="padding: 0 10px">
                      <img
                        style="height: 400px; cursor: pointer; margin: 0 auto; display: block"
                        :src="item.pic_url"
                      />
                    </div>
                    <template #reference>
                      <el-image
                        style="width: 120px; height: auto; cursor: pointer"
                        :src="item.pic_url"
                        fit="cover"
                      />
                    </template>
                  </el-popover>
                  <el-popover
                    v-if="item.expression_url"
                    trigger="hover"
                    placement="top"
                    width="auto"
                  >
                    <div style="padding: 0 10px">
                      <img
                        style="height: 400px; cursor: pointer; margin: 0 auto; display: block"
                        :src="item.expression_url"
                      />
                    </div>
                    <template #reference>
                      <el-image
                        style="width: 120px; height: auto; cursor: pointer"
                        :src="item.expression_url"
                        fit="cover"
                      />
                    </template>
                  </el-popover>
                  <!-- 图片敏感词 -->
                  <el-tooltip
                    v-if="
                      item.comment_pic_risk_result && item.comment_pic_risk_result.resultCode === 1
                    "
                    class="box-item"
                    effect="light"
                    :content="item.comment_pic_risk_result.resultMsg"
                    placement="top"
                  >
                    <el-button
                      type="danger"
                      plain
                      size="small"
                      color="#F65520"
                      style="margin-left: 5px; padding: 0 3px; height: 18px"
                    >
                      敏
                    </el-button>
                  </el-tooltip>
                  <SpammerIcon v-if="item.spammer" />
                  <!-- <img class="pic" v-if="item.pic_url" :src="item.pic_url" alt="">
                  <img class="pic" v-if="item.expression_url" :src="item.expression_url" alt=""> -->
                  <iframe
                    v-if="item.activity_url"
                    class="ifrBox"
                    scrolling="no"
                    :src="item.activity_url"
                    frameborder="0"
                    width="300"
                    :height="item.activity_ratio_height * (300 / item.activity_ratio_width)"
                    @error="handleIframeError"
                  ></iframe>
                </div>
                <div class="footer">
                  <div class="time">
                    <span>{{ handleTime2(item.created_at) }}</span>
                    {{ item.location }}
                  </div>
                  <div class="f-right">
                    <div class="reply children" @click="level2_handleReply(item, index)">回复</div>
                    <div class="like children" @click="setLike(item)">点赞</div>
                    <div class="hot children" @click="setHot(item, 'OFF')">取消热评</div>
                    <div class="del children" @click="handleDel(item, 1, 1)">删除</div>
                  </div>
                </div>
                <div v-if="item.sub_has_more" class="reply-list">
                  <div
                    v-for="(item2, index) in item.sub_comment_list"
                    :key="item2.id"
                    class="reply-child"
                  >
                    <div class="reply-head">
                      <div class="reply-avatar">
                        <img :src="item2.portrait_url" alt="" />
                      </div>
                      <div class="reply-right">
                        <div class="reply-name">
                          <SpammerMarker
                            v-model:spammer="item2.spammer"
                            :disabled="commentChoise !== 2"
                            :user-data="{
                              user_name: item2.comment_user_name,
                              user_id: item2.account_id || item2.comment_user_id
                            }"
                          >
                            <template #reference>
                              <span>{{ item2.comment_user_name }}</span>
                            </template>
                          </SpammerMarker>
                          <svg-icon
                            v-if="item2.parent_nick_name"
                            name="icon-to"
                            style="margin: 0 10px; width: 10px; height: 10px"
                          />
                          <div v-if="item2.parent_nick_name" class="reply-avatar">
                            <img :src="item2.parent_portrait_url" alt="" />
                          </div>
                          <span v-if="item2.parent_nick_name"> {{ item2.parent_nick_name }}</span>
                          <el-tooltip
                            v-if="item2.vest_mark"
                            class="box-item"
                            effect="dark"
                            :content="item2.vest_admin_name"
                            placement="top"
                          >
                            <div class="guide">引导</div>
                          </el-tooltip>
                        </div>
                      </div>
                    </div>
                    <div class="reply-detail">
                      <span v-html="item2.content"></span>
                      <!-- 敏感词 -->
                      <el-tooltip
                        v-if="item2.sensitive_word"
                        class="box-item"
                        effect="light"
                        :content="item2.sensitive_word"
                        placement="top"
                      >
                        <el-button
                          type="danger"
                          plain
                          size="small"
                          color="#F65520"
                          style="margin-left: 5px; padding: 0 3px; height: 18px"
                        >
                          敏
                        </el-button>
                      </el-tooltip>
                      <el-tooltip
                        v-if="item2.delete_reason"
                        class="box-item"
                        effect="light"
                        :content="item2.delete_reason"
                        placement="top"
                      >
                        <el-button
                          type="danger"
                          plain
                          size="small"
                          color="#FF8920"
                          style="margin-left: 5px; padding: 0 3px; height: 18px"
                        >
                          治
                        </el-button>
                      </el-tooltip>
                      <!-- 图片评论 -->
                      <br v-if="item2.content && (item2.pic_url || item2.expression_url)" />
                      <el-popover v-if="item2.pic_url" trigger="hover" placement="top" width="auto">
                        <div style="padding: 0 10px">
                          <img
                            style="height: 400px; cursor: pointer; margin: 0 auto; display: block"
                            :src="item2.pic_url"
                          />
                        </div>
                        <template #reference>
                          <el-image
                            style="width: 120px; height: auto; cursor: pointer"
                            :src="item2.pic_url"
                            fit="cover"
                          />
                        </template>
                      </el-popover>
                      <el-popover
                        v-if="item2.expression_url"
                        trigger="hover"
                        placement="top"
                        width="auto"
                      >
                        <div style="padding: 0 10px">
                          <img
                            style="height: 400px; cursor: pointer; margin: 0 auto; display: block"
                            :src="item2.expression_url"
                          />
                        </div>
                        <template #reference>
                          <el-image
                            style="width: 120px; height: auto; cursor: pointer"
                            :src="item2.expression_url"
                            fit="cover"
                          />
                        </template>
                      </el-popover>
                      <!-- 图片敏感词 -->
                      <el-tooltip
                        v-if="
                          item2.comment_pic_risk_result &&
                          item2.comment_pic_risk_result.resultCode === 1
                        "
                        class="box-item"
                        effect="light"
                        :content="item2.comment_pic_risk_result.resultMsg"
                        placement="top"
                      >
                        <el-button
                          type="danger"
                          plain
                          size="small"
                          color="#F65520"
                          style="margin-left: 5px; padding: 0 3px; height: 18px"
                        >
                          敏
                        </el-button>
                      </el-tooltip>
                      <SpammerIcon v-if="item2.spammer" />
                      <WhiteUserIcon v-if="item2.white_list_user" />
                    </div>
                    <div class="footer">
                      <div class="time">
                        <span>{{ handleTime2(item2.created_at) }}</span>
                        {{ item2.location }}
                      </div>
                      <div v-if="item2.status === 1" class="f-right">
                        <div class="reply children" @click="level3_handleReply(item2, index, item)">
                          回复
                        </div>
                        <div class="like children" @click="setLike(item2)">点赞</div>
                        <div class="del children" @click="handleDel(item2, 1, 2, item)">删除</div>
                      </div>
                      <div v-else-if="item2.status === 0" class="f-right">
                        <div class="pass children" @click="handlePass(item2, 1, 2, item)">通过</div>
                        <div class="del children" @click="handleDel(item2, 1, 2, item)">删除</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  v-if="item.sub_count > 0 && !item.showChild"
                  class="show-more"
                  @click="getMoreHotComment(item.id)"
                >
                  <span class="line"></span>
                  <span>展开{{ item.sub_count }}条评论</span>
                  <svg-icon name="icon-more" />
                </div>
                <div
                  v-if="item.sub_count > 0 && item.showChild"
                  class="show-more"
                  @click="hotHide(item.id)"
                >
                  <span class="line"></span>
                  <span>收起</span>
                  <svg-icon name="icon-up" />
                </div>
                <el-divider />
              </div>
              <!-- 普通评论 -->
              <div v-for="(item, index) in comment.list" :key="item.id" class="child">
                <div class="head">
                  <div class="avatar">
                    <img :src="item.portrait_url" alt="" />
                  </div>
                  <div class="right">
                    <div class="name">
                      <SpammerMarker
                        v-model:spammer="item.spammer"
                        :disabled="commentChoise !== 2"
                        :user-data="{
                          user_name: item.comment_user_name,
                          user_id: item.account_id || item.comment_user_id
                        }"
                      >
                        <template #reference>
                          <span>{{ item.comment_user_name }}</span>
                        </template>
                      </SpammerMarker>
                    </div>
                    <el-tooltip
                      v-if="item.vest_mark"
                      class="box-item"
                      effect="dark"
                      :content="item.vest_admin_name"
                      placement="top"
                    >
                      <div class="guide">引导</div>
                    </el-tooltip>
                  </div>
                </div>
                <div class="comment-detail">
                  <span v-if="item.top" class="topping">置顶</span>
                  <span v-html="item.content"></span>
                  <!-- 敏感词 -->
                  <el-tooltip
                    v-if="item.sensitive_word"
                    class="box-item"
                    effect="light"
                    :content="item.sensitive_word"
                    placement="top"
                  >
                    <el-button
                      type="danger"
                      plain
                      size="small"
                      color="#F65520"
                      style="margin-left: 5px; padding: 0 3px; height: 18px"
                    >
                      敏
                    </el-button>
                  </el-tooltip>
                  <el-tooltip
                    v-if="item.delete_reason"
                    class="box-item"
                    effect="light"
                    :content="item.delete_reason"
                    placement="top"
                  >
                    <el-button
                      type="danger"
                      plain
                      size="small"
                      color="#FF8920"
                      style="margin-left: 5px; padding: 0 3px; height: 18px"
                    >
                      治
                    </el-button>
                  </el-tooltip>
                  <br v-if="item.content && (item.pic_url || item.expression_url)" />
                  <!-- 图片评论 -->
                  <el-popover v-if="item.pic_url" trigger="hover" placement="top" width="auto">
                    <div style="padding: 0 10px">
                      <img
                        style="height: 400px; cursor: pointer; margin: 0 auto; display: block"
                        :src="item.pic_url"
                      />
                    </div>
                    <template #reference>
                      <el-image
                        style="width: 120px; height: auto; cursor: pointer"
                        :src="item.pic_url"
                        fit="cover"
                      />
                    </template>
                  </el-popover>
                  <el-popover
                    v-if="item.expression_url"
                    trigger="hover"
                    placement="top"
                    width="auto"
                  >
                    <div style="padding: 0 10px">
                      <img
                        style="height: 400px; cursor: pointer; margin: 0 auto; display: block"
                        :src="item.expression_url"
                      />
                    </div>
                    <template #reference>
                      <el-image
                        style="width: 120px; height: auto; cursor: pointer"
                        :src="item.expression_url"
                        fit="cover"
                      />
                    </template>
                  </el-popover>
                  <!-- 图片敏感词 -->
                  <el-tooltip
                    v-if="
                      item.comment_pic_risk_result && item.comment_pic_risk_result.resultCode === 1
                    "
                    class="box-item"
                    effect="light"
                    :content="item.comment_pic_risk_result.resultMsg"
                    placement="top"
                  >
                    <el-button
                      type="danger"
                      plain
                      size="small"
                      color="#F65520"
                      style="margin-left: 5px; padding: 0 3px; height: 18px"
                    >
                      敏
                    </el-button>
                  </el-tooltip>
                  <SpammerIcon v-if="item.spammer" />
                  <WhiteUserIcon v-if="item.white_list_user" />
                  <!-- <img class="pic" v-if="item.pic_url" :src="item.pic_url" alt="">
                  <img class="pic" v-if="item.expression_url" :src="item.expression_url" alt=""> -->
                  <iframe
                    v-if="item.activity_url"
                    class="ifrBox"
                    scrolling="no"
                    :src="item.activity_url"
                    frameborder="0"
                    width="300"
                    :height="item.activity_ratio_height * (300 / item.activity_ratio_width)"
                    @error="handleIframeError"
                  ></iframe>
                </div>
                <div class="footer">
                  <div class="time">
                    <span>{{ handleTime2(item.created_at) }}</span>
                    {{ item.location }}
                  </div>
                  <div v-if="item.status === 1" class="f-right">
                    <div class="reply children" @click="level2_handleReply(item, index)">回复</div>
                    <div class="like children" @click="setLike(item)">点赞</div>
                    <div class="hot children" @click="setHot(item, 'ON')">设为热评</div>
                    <div class="del children" @click="handleDel(item, 2, 1)">删除</div>
                  </div>
                  <div v-else-if="item.status === 0" class="f-right">
                    <div class="pass children" @click="handlePass(item, 2, 1, index)">通过</div>
                    <div class="del children" @click="handleDel(item, 2, 1)">删除</div>
                  </div>
                </div>
                <div v-if="item.sub_has_more" class="reply-list">
                  <div
                    v-for="(item2, index2) in item.sub_comment_list"
                    :key="item2.id"
                    class="reply-child"
                  >
                    <div class="reply-head">
                      <div class="reply-avatar">
                        <img :src="item2.portrait_url" alt="" />
                      </div>
                      <div class="reply-right">
                        <div class="reply-name">
                          <SpammerMarker
                            v-model:spammer="item2.spammer"
                            :disabled="commentChoise !== 2"
                            :user-data="{
                              user_name: item2.comment_user_name,
                              user_id: item2.account_id || item2.comment_user_id
                            }"
                          >
                            <template #reference>
                              <span>{{ item2.comment_user_name }}</span>
                            </template>
                          </SpammerMarker>
                          <svg-icon
                            v-if="item2.parent_nick_name"
                            name="icon-to"
                            style="margin: 0 10px; width: 10px; height: 10px"
                          />
                          <div v-if="item2.parent_nick_name" class="reply-avatar">
                            <img :src="item2.parent_portrait_url" alt="" />
                          </div>
                          <span v-if="item2.parent_nick_name"> {{ item2.parent_nick_name }}</span>
                          <el-tooltip
                            v-if="item2.vest_mark"
                            class="box-item"
                            effect="dark"
                            :content="item2.vest_admin_name"
                            placement="top"
                          >
                            <div class="guide">引导</div>
                          </el-tooltip>
                        </div>
                      </div>
                    </div>
                    <div class="reply-detail">
                      <span v-html="item2.content"></span>
                      <!-- 敏感词 -->
                      <el-tooltip
                        v-if="item2.sensitive_word"
                        class="box-item"
                        effect="light"
                        :content="item2.sensitive_word"
                        placement="top"
                      >
                        <el-button
                          type="danger"
                          plain
                          size="small"
                          color="#F65520"
                          style="margin-left: 5px; padding: 0 3px; height: 18px"
                        >
                          敏
                        </el-button>
                      </el-tooltip>
                      <el-tooltip
                        v-if="item2.delete_reason"
                        class="box-item"
                        effect="light"
                        :content="item2.delete_reason"
                        placement="top"
                      >
                        <el-button
                          type="danger"
                          plain
                          size="small"
                          color="#FF8920"
                          style="margin-left: 5px; padding: 0 3px; height: 18px"
                        >
                          治
                        </el-button>
                      </el-tooltip>
                      <br v-if="item2.content && (item2.pic_url || item2.expression_url)" />
                      <!-- 图片评论 -->
                      <el-popover v-if="item2.pic_url" trigger="hover" placement="top" width="auto">
                        <div style="padding: 0 10px">
                          <img
                            style="height: 400px; cursor: pointer; margin: 0 auto; display: block"
                            :src="item2.pic_url"
                          />
                        </div>
                        <template #reference>
                          <el-image
                            style="width: 120px; height: auto; cursor: pointer"
                            :src="item2.pic_url"
                            fit="cover"
                          />
                        </template>
                      </el-popover>
                      <el-popover
                        v-if="item2.expression_url"
                        trigger="hover"
                        placement="top"
                        width="auto"
                      >
                        <div style="padding: 0 10px">
                          <img
                            style="height: 400px; cursor: pointer; margin: 0 auto; display: block"
                            :src="item2.expression_url"
                          />
                        </div>
                        <template #reference>
                          <el-image
                            style="width: 120px; height: auto; cursor: pointer"
                            :src="item2.expression_url"
                            fit="cover"
                          />
                        </template>
                      </el-popover>
                      <!-- 图片敏感词 -->
                      <el-tooltip
                        v-if="
                          item2.comment_pic_risk_result &&
                          item2.comment_pic_risk_result.resultCode === 1
                        "
                        class="box-item"
                        effect="light"
                        :content="item2.comment_pic_risk_result.resultMsg"
                        placement="top"
                      >
                        <el-button
                          type="danger"
                          plain
                          size="small"
                          color="#F65520"
                          style="margin-left: 5px; padding: 0 3px; height: 18px"
                        >
                          敏
                        </el-button>
                      </el-tooltip>
                      <SpammerIcon v-if="item2.spammer" />
                      <WhiteUserIcon v-if="item2.white_list_user" />
                    </div>
                    <div class="footer">
                      <div class="time">
                        <span>{{ handleTime2(item2.created_at) }}</span>
                        {{ item2.location }}
                      </div>
                      <div v-if="item2.status === 1" class="f-right">
                        <div class="reply children" @click="level3_handleReply(item2, index, item)">
                          回复
                        </div>
                        <div class="like children" @click="setLike(item2)">点赞</div>
                        <div class="del children" @click="handleDel(item2, 2, 2, index)">删除</div>
                      </div>
                      <div v-else-if="item2.status === 0" class="f-right">
                        <div class="pass children" @click="handlePass(item2, 2, 2, index, index2)">
                          通过
                        </div>
                        <div class="del children" @click="handleDel(item2, 2, 2, index)">删除</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  v-if="item.sub_count > 0 && !item.showChild"
                  class="show-more"
                  @click="getMoreComment(item.id)"
                >
                  <span class="line"></span>
                  <span>展开{{ item.sub_count }}条评论</span>
                  <svg-icon name="icon-more" />
                </div>
                <div
                  v-if="item.sub_count > 0 && item.showChild"
                  class="show-more"
                  @click="commendHide(item.id)"
                >
                  <span class="line"></span>
                  <span>收起</span>
                  <svg-icon name="icon-up" />
                </div>
                <el-divider />
              </div>
            </div>
            <div v-else-if="commentChoise === 2 && adopt.list.length" class="comment-list">
              <div v-for="item in adopt.list" :key="item.id" class="child">
                <div class="head">
                  <div class="avatar">
                    <img :src="item.portrait_url" alt="" />
                  </div>
                  <div class="right">
                    <div class="name">
                      <SpammerMarker
                        v-model:spammer="item.spammer"
                        :disabled="commentChoise !== 2"
                        :user-data="{
                          user_name: item.comment_user_name,
                          user_id: item.account_id || item.comment_user_id
                        }"
                      >
                        <template #reference>
                          <span>{{ item.comment_user_name }}</span>
                        </template>
                      </SpammerMarker>
                    </div>
                  </div>
                </div>
                <div class="comment-detail">
                  <div
                    v-if="item.reply_info && item.reply_info.reply_info_content"
                    class="reply-content"
                  >
                    <span v-html="item.reply_info.reply_info_content"></span>
                    <el-tooltip
                      v-if="item.reply_info.sensitive_word"
                      class="box-item"
                      effect="light"
                      :content="item.reply_info.sensitive_word"
                      placement="top"
                    >
                      <el-button
                        type="danger"
                        plain
                        size="small"
                        color="#F65520"
                        style="margin-left: 5px; padding: 0 3px; height: 18px"
                      >
                        敏
                      </el-button>
                    </el-tooltip>
                    <el-tooltip
                      v-if="item.reply_info.delete_reason"
                      class="box-item"
                      effect="light"
                      :content="item.reply_info.delete_reason"
                      placement="top"
                    >
                      <el-button
                        type="danger"
                        plain
                        size="small"
                        color="#FF8920"
                        style="margin-left: 5px; padding: 0 3px; height: 18px"
                      >
                        治
                      </el-button>
                    </el-tooltip>
                    <SpammerIcon v-if="item.reply_info.spammer" />
                    <!-- <WhiteUserIcon v-if="item.white_list_user" /> -->
                  </div>
                  <span v-html="item.content"></span>
                  <!-- 敏感词 -->
                  <el-tooltip
                    v-if="item.sensitive_word"
                    class="box-item"
                    effect="light"
                    :content="item.sensitive_word"
                    placement="top"
                  >
                    <el-button
                      type="danger"
                      plain
                      size="small"
                      color="#F65520"
                      style="margin-left: 5px; padding: 0 3px; height: 18px"
                    >
                      敏
                    </el-button>
                  </el-tooltip>
                  <el-tooltip
                    v-if="item.delete_reason"
                    class="box-item"
                    effect="light"
                    :content="item.delete_reason"
                    placement="top"
                  >
                    <el-button
                      type="danger"
                      plain
                      size="small"
                      color="#FF8920"
                      style="margin-left: 5px; padding: 0 3px; height: 18px"
                    >
                      治
                    </el-button>
                  </el-tooltip>
                  <br v-if="item.content && (item.pic_url || item.expression_url)" />
                  <!-- 图片评论 -->
                  <el-popover v-if="item.pic_url" trigger="hover" placement="top" width="auto">
                    <div style="padding: 0 10px">
                      <img
                        style="height: 400px; cursor: pointer; margin: 0 auto; display: block"
                        :src="item.pic_url"
                      />
                    </div>
                    <template #reference>
                      <el-image
                        style="width: 120px; height: auto; cursor: pointer"
                        :src="item.pic_url"
                        fit="cover"
                      />
                    </template>
                  </el-popover>
                  <el-popover
                    v-if="item.expression_url"
                    trigger="hover"
                    placement="top"
                    width="auto"
                  >
                    <div style="padding: 0 10px">
                      <img
                        style="height: 400px; cursor: pointer; margin: 0 auto; display: block"
                        :src="item.expression_url"
                      />
                    </div>
                    <template #reference>
                      <el-image
                        style="width: 120px; height: auto; cursor: pointer"
                        :src="item.expression_url"
                        fit="cover"
                      />
                    </template>
                  </el-popover>
                  <!-- 图片敏感词 -->
                  <el-tooltip
                    v-if="
                      item.comment_pic_risk_result && item.comment_pic_risk_result.resultCode === 1
                    "
                    class="box-item"
                    effect="light"
                    :content="item.comment_pic_risk_result.resultMsg"
                    placement="top"
                  >
                    <el-button
                      type="danger"
                      plain
                      size="small"
                      color="#F65520"
                      style="margin-left: 5px; padding: 0 3px; height: 18px"
                    >
                      敏
                    </el-button>
                  </el-tooltip>
                  <SpammerIcon v-if="item.spammer" />
                  <WhiteUserIcon v-if="item.white_list_user" />
                  <!-- <img class="pic" v-if="item.pic_url" :src="item.pic_url" alt="">
                  <img class="pic" v-if="item.expression_url" :src="item.expression_url" alt=""> -->
                </div>
                <div class="footer">
                  <div class="time">
                    <span>{{ item.created_at }}</span>
                    {{ item.location }}
                  </div>
                  <div class="f-right">
                    <div class="pass children" @click="adoptPass(item)">通过</div>
                    <div class="del children" @click="adoptDel(item)">删除</div>
                  </div>
                </div>
                <el-divider />
              </div>
            </div>
            <div v-else-if="commentChoise === 3 && del.list.length" class="comment-list">
              <div v-for="item in del.list" :key="item.id" class="child">
                <div class="head">
                  <div class="avatar">
                    <img :src="item.portrait_url" alt="" />
                  </div>
                  <div class="right">
                    <div class="name">
                      <SpammerMarker
                        v-model:spammer="item.spammer"
                        :disabled="commentChoise !== 2"
                        :user-data="{
                          user_name: item.comment_user_name,
                          user_id: item.account_id || item.comment_user_id
                        }"
                      >
                        <template #reference>
                          <span>{{ item.comment_user_name }}</span>
                        </template>
                      </SpammerMarker>
                    </div>
                  </div>
                </div>
                <div class="comment-detail">
                  <div
                    v-if="item.reply_info && item.reply_info.reply_info_content"
                    class="reply-content"
                  >
                    <span v-html="item.reply_info.reply_info_content"></span>
                    <el-tooltip
                      v-if="item.reply_info.sensitive_word"
                      class="box-item"
                      effect="light"
                      :content="item.reply_info.sensitive_word"
                      placement="top"
                    >
                      <el-button
                        type="danger"
                        plain
                        size="small"
                        color="#F65520"
                        style="margin-left: 5px; padding: 0 3px; height: 18px"
                      >
                        敏
                      </el-button>
                    </el-tooltip>
                    <el-tooltip
                      v-if="item.reply_info.delete_reason"
                      class="box-item"
                      effect="light"
                      :content="item.reply_info.delete_reason"
                      placement="top"
                    >
                      <el-button
                        type="danger"
                        plain
                        size="small"
                        color="#FF8920"
                        style="margin-left: 5px; padding: 0 3px; height: 18px"
                      >
                        治
                      </el-button>
                    </el-tooltip>
                    <SpammerIcon v-if="item.reply_info.spammer" />
                    <!-- <WhiteUserIcon v-if="item.white_list_user" /> -->
                  </div>
                  <span v-html="item.content"></span>
                  <!-- 敏感词 -->
                  <el-tooltip
                    v-if="item.sensitive_word"
                    class="box-item"
                    effect="light"
                    :content="item.sensitive_word"
                    placement="top"
                  >
                    <el-button
                      type="danger"
                      plain
                      size="small"
                      color="#F65520"
                      style="margin-left: 5px; padding: 0 3px; height: 18px"
                    >
                      敏
                    </el-button>
                  </el-tooltip>
                  <el-tooltip
                    v-if="item.delete_reason"
                    class="box-item"
                    effect="light"
                    :content="item.delete_reason"
                    placement="top"
                  >
                    <el-button
                      type="danger"
                      plain
                      size="small"
                      color="#FF8920"
                      style="margin-left: 5px; padding: 0 3px; height: 18px"
                    >
                      治
                    </el-button>
                  </el-tooltip>
                  <br v-if="item.content && (item.pic_url || item.expression_url)" />
                  <!-- 图片评论 -->
                  <el-popover v-if="item.pic_url" trigger="hover" placement="top" width="auto">
                    <div style="padding: 0 10px">
                      <img
                        style="height: 400px; cursor: pointer; margin: 0 auto; display: block"
                        :src="item.pic_url"
                      />
                    </div>
                    <template #reference>
                      <el-image
                        style="width: 120px; height: auto; cursor: pointer"
                        :src="item.pic_url"
                        fit="cover"
                      />
                    </template>
                  </el-popover>
                  <el-popover
                    v-if="item.expression_url"
                    trigger="hover"
                    placement="top"
                    width="auto"
                  >
                    <div style="padding: 0 10px">
                      <img
                        style="height: 400px; cursor: pointer; margin: 0 auto; display: block"
                        :src="item.expression_url"
                      />
                    </div>
                    <template #reference>
                      <el-image
                        style="width: 120px; height: auto; cursor: pointer"
                        :src="item.expression_url"
                        fit="cover"
                      />
                    </template>
                  </el-popover>
                  <!-- 图片敏感词 -->
                  <el-tooltip
                    v-if="
                      item.comment_pic_risk_result && item.comment_pic_risk_result.resultCode === 1
                    "
                    class="box-item"
                    effect="light"
                    :content="item.comment_pic_risk_result.resultMsg"
                    placement="top"
                  >
                    <el-button
                      type="danger"
                      plain
                      size="small"
                      color="#F65520"
                      style="margin-left: 5px; padding: 0 3px; height: 18px"
                    >
                      敏
                    </el-button>
                  </el-tooltip>
                  <SpammerIcon v-if="item.spammer" />
                  <WhiteUserIcon v-if="item.white_list_user" />
                  <!-- <img class="pic" v-if="item.pic_url" :src="item.pic_url" alt="">
                  <img class="pic" v-if="item.expression_url" :src="item.expression_url" alt=""> -->
                  <iframe
                    v-if="item.activity_url"
                    class="ifrBox"
                    scrolling="no"
                    :src="item.activity_url"
                    frameborder="0"
                    width="300"
                    :height="item.activity_ratio_height * (300 / item.activity_ratio_width)"
                    @error="handleIframeError"
                  ></iframe>
                </div>
                <div class="footer">
                  <div class="time">
                    <span>{{ item.created_at }}</span>
                    {{ item.location }}
                  </div>
                  <div class="f-right">
                    <div
                      v-if="item.is_user_delete !== 1"
                      class="del2 children"
                      @click="handleDelAdopt(item)"
                    >
                      撤销至待审
                    </div>
                    <div
                      v-if="item.is_user_delete !== 1"
                      class="del2 children"
                      @click="handleDelPass(item)"
                    >
                      撤销并通过
                    </div>
                  </div>
                </div>

                <el-divider />
              </div>
            </div>
            <div v-else class="comment-list null">
              <img src="@/assets/images/circle/noComment.png" alt="" />
              <div class="font">暂无评论</div>
            </div>
          </div>
          <Reply
            v-if="commentChoise === 1"
            ref="replyRef"
            :reply-data="replyData"
            :comment="comment"
            :reply-hierarchy="replyHierarchy"
            @replySuccess="replySuccess"
            @click="replySubCount = '-1'"
          ></Reply>
        </div>
      </div>
    </div>
    <el-dialog v-model="cheatShow" title="马甲号点赞" width="35%" align-center :show-close="false">
      <span style="margin-right: 20px">请选择需要加权<span style="color: red">至</span>多少赞</span>
      <el-input-number
        v-model="ehatVal"
        :max="minEhatVal + 50"
        :min="minEhatVal"
        size="default"
        @keydown="channelInputLimit"
      ></el-input-number>
      <template #footer>
        <span class="dialog-footer">
          <el-button style="margin-right: 20px" @click="cheatShow = false">取消</el-button>
          <el-button type="primary" :loading="loading" @click="handleAddCheatNum"> 提交 </el-button>
        </span>
      </template>
    </el-dialog>
  </AbsoluteContainer>
</template>
<script setup>
import useClipboard from 'vue-clipboard3'
import Detail from './modules/details.vue'
import { ref, onMounted } from 'vue'
import { Search } from '@element-plus/icons'
import { ElMessage, ElMessageBox, stepProps } from 'element-plus'
import { Delete, Select } from '@element-plus/icons-vue'
import { requests } from '@/api/business/circle.js'
import Reply from '@/components/reply/index.vue'
import { useRoute } from 'vue-router'
import SpammerIcon from '@/components/SpammerIcon/index.vue'
import WhiteUserIcon from '@/components/WhiteUserIcon/index.vue'
import SpammerMarker from '@/components/SpammerMarker/index.vue'

const replayList = ref([])
const detail = ref({
  avatar: '',
  name: '',
  des: '',
  place: '',
  content: '',
  picList: []
})
const isChildShow = ref(false)
const childId = ref('')
const commentChoise = ref(1)
const circlelist = ref([])
// 左侧列表
const left = ref({
  current: 0,
  size: 100,
  circle_id: '',
  article_title: '',
  comment_state: 1,
  search_type: 2 // 1 根据稿件id 2根据稿件标题
})
const articleList = ref([])
const leftShowTip = ref(false)
// 左侧加载更多
const leftLoadMore = ref(true)
// 左侧加载
const leftLoading = ref(false)
// 评论列表相关
const comment = ref({
  id: '',
  list: [],
  hotList: [],
  size: 1,
  sort_number: 0,
  adoptNumber: 0,
  showTips: false
})
const adopt = ref({
  list: [],
  current: 0,
  size: 50,
  loadMore: true
})
const del = ref({
  list: [],
  current: 0,
  size: 50,
  loadMore: true
})
const loading = ref(false)
// reply需要参数
const replyRef = ref(null)
const replyData = ref(null)
const replyHierarchy = ref({
  second_comment_id: '',
  user_placeholder: '',
  parent_comment_id: '',
  top_comment_id: '',
  comment_level: '',
  index: '',
  id: '',
  content: ''
})
const replySubCount = ref()
const replyId = ref()
// 右侧无限加载开关
const infiniteDisabled = ref(false)
const disabledShow = ref(false)
const deleteToPass = ref(false)
const disableData = ref([])
const commentIds = ref([])
// 复制
const { toClipboard } = useClipboard()
const copyAddress = asyncrow => {
  try {
    toClipboard(asyncrow)
    ElMessage({
      type: 'success',
      message: '复制成功！'
    })
  } catch (e) {
    // 复制不成功的时候控制台输出错误
    console.error(e)
  }
}
// 回复或创建成功后
const replySuccess = () => {
  replyRef.value.replyFlag = false
  if (replySubCount.value >= 0) {
    getMoreHotComment(replyId.value)
    getMoreComment(replyId.value)
  } else {
    getHot()
    getComment()
  }
}
// 二级评论
const level2_handleReply = (val, index) => {
  replyRef.value.drawer = true
  replyHierarchy.value.second_comment_id = ''
  replyHierarchy.value.user_placeholder = val.comment_user_name
  replyHierarchy.value.parent_comment_id = val.id
  replyHierarchy.value.top_comment_id = val.id
  replyHierarchy.value.comment_level = 2
  replyHierarchy.value.index = index
  replyHierarchy.value.id = val.id
  replyHierarchy.value.content = val.content
  replySubCount.value = val.sub_count
  replyId.value = val.id
  replyRef.value.replyFlag = true
}
// 三级评论
const level3_handleReply = (val, index, item) => {
  replyRef.value.drawer = true
  replyHierarchy.value.user_placeholder = val.comment_user_name
  replyHierarchy.value.parent_comment_id = val.id
  replyHierarchy.value.top_comment_id = val.top_comment_id
  val.comment_level == 2
    ? (replyHierarchy.value.second_comment_id = val.id)
    : (replyHierarchy.value.second_comment_id = val.second_comment_id)
  replyHierarchy.value.comment_level = 3
  replyHierarchy.value.index = index
  replyHierarchy.value.id = val.id
  replyHierarchy.value.content = val.content
  replySubCount.value = item.sub_count
  replyId.value = item.id
  replyRef.value.replyFlag = true
}

const route = useRoute()
console.log('route.params===>', route.params)
let searchArticleId = route.query.articleId

onMounted(() => {
  getCircleList()
  getLeftAdoptNum()
})

// 获取圈子
function getCircleList() {
  requests.circleList({}).then(res => {
    circlelist.value = circlelist.value.concat(res.data.circle_list)
  })
}
// 搜索
function handSearch() {
  leftLoadMore.value = true
  left.value.current = 0
  articleList.value = []
  leftLoad()
}
// 左侧触底加载
function leftLoad() {
  if (!leftLoadMore.value || leftLoading.value) return
  left.value.current = left.value.current + 1
  // 判断当前是否是搜索稿件id
  if (searchArticleId) {
    left.value.article_title = searchArticleId
    left.value.search_type = 1
    searchArticleId = ''
  } else {
    left.value.search_type = 2
  }

  leftLoading.value = true
  let func = left.value.comment_state == 1 ? 'getList' : 'getList2'
  requests[func](left.value)
    .then(res => {
      if (res.data.release_list.records.length < 100 || left.value.comment_state == 0) {
        leftLoadMore.value = false
      }
      leftLoading.value = false
      if (!isChildShow.value || left.value.current == 1) {
        let firstData = res.data.release_list.records[0]
        showDetail(firstData)
      }
      articleList.value = articleList.value.concat(res.data.release_list.records)
    })
    .catch(v => {
      leftLoadMore.value = false
      leftLoading.value = false
    })
}
// 获取左侧待审核评论数量
function getLeftAdoptNum() {
  requests
    .getAdoptNum({
      circle_id: left.value.circle_id
    })
    .then(res => {
      if (res.data.count > 0) {
        leftShowTip.value = true
      } else {
        leftShowTip.value = false
      }
    })
}
// tab切换
function tabChange(val) {
  leftLoadMore.value = true
  left.value.comment_state = val
  left.value.current = 0
  articleList.value = []
  leftLoading.value = false
  leftLoad()
  getLeftAdoptNum()
}
// 右侧tab切换
function rightTabChange(val) {
  commentChoise.value = val
  if (val === 1) {
    console.log(comment.value.adoptNumber)
    comment.value = {
      id: comment.value.id,
      list: [],
      hotList: [],
      size: 1,
      sort_number: 0,
      loadMore: true,
      adoptNumber: comment.value.adoptNumber,
      showTips: comment.value.showTips
    }
    getComment()
    getHot()
  } else if (val === 2) {
    adopt.value = {
      list: [],
      current: 0,
      size: 50,
      loadMore: true
    }
    getAdoptList()
  } else {
    del.value = {
      list: [],
      current: 0,
      size: 50,
      loadMore: true
    }
    getDelList()
  }
}
// 选择圈子
function selectChange() {
  leftLoadMore.value = true
  left.value.current = 0
  articleList.value = []
  getLeftAdoptNum()
  leftLoad()
}
// 查看详情
function showDetail(item) {
  // reply需要参数
  replyData.value = item
  if (childId.value === item.id) {
    return
  }

  childId.value = item.id
  let id = item.original_id ? item.original_id : item.id
  if (item.uuid || item.ugc_uuid) {
    id = item.uuid || item.ugc_uuid
  }
  requests.getDetails({ article_id: id }).then(res => {
    detail.value = res.data.article
    detail.value.isRich = isRichText(detail.value.content)
    isChildShow.value = true
  })

  comment.value = {
    id: comment.value.id,
    list: [],
    hotList: [],
    size: 10000,
    sort_number: 0,
    loadMore: true,
    adoptNumber: 0,
    showTips: false
  }
  adopt.value = {
    list: [],
    current: 0,
    size: 50,
    loadMore: true
  }
  del.value = {
    list: [],
    current: 0,
    size: 20,
    loadMore: true
  }
  commentChoise.value = 1
  getComment(id)
  getHot(id)
  getAdoptNum()
  getAdoptList()
  getDelList()
}
// 右侧触底加载
function loadRight() {
  if (commentChoise.value === 1) {
    infiniteDisabled.value = true
    return
  }
  if (commentChoise.value === 2) {
    if (!adopt.value.loadMore) {
      return
    }
    getAdoptList()
  }
  if (commentChoise.value === 3) {
    if (!del.value.loadMore) {
      return
    }
    getDelList()
  }
}
// 获取已删除列表
function getDelList() {
  if (loading.value) return
  del.value.current = del.value.current + 1
  loading.value = true
  infiniteDisabled.value = false
  requests
    .getAdopt({
      article_id: comment.value.id,
      current: del.value.current,
      size: del.value.size,
      state: 2
    })
    .then(res => {
      loading.value = false
      if (!res.data.list.length) {
        infiniteDisabled.value = true
        del.value.loadMore = false
        return
      }
      res.data.list.forEach(v => {
        if (v.comment_pic_risk_result) {
          v.comment_pic_risk_result = JSON.parse(v.comment_pic_risk_result)
        } else {
          v.comment_pic_risk_result = {}
        }
      })
      del.value.list = del.value.list.concat(res.data.list)
    })
    .catch(v => {
      loading.value = false
    })
}
// 获取待审核列表
function getAdoptList() {
  if (loading.value) return
  loading.value = true
  infiniteDisabled.value = false
  adopt.value.current = adopt.value.current + 1
  requests
    .getAdopt({
      article_id: comment.value.id,
      current: adopt.value.current,
      size: adopt.value.size,
      state: 0
    })
    .then(res => {
      loading.value = false
      if (!res.data.list.length) {
        infiniteDisabled.value = true
        adopt.value.loadMore = false
        return
      }
      res.data.list.forEach(v => {
        if (v.comment_pic_risk_result) {
          v.comment_pic_risk_result = JSON.parse(v.comment_pic_risk_result)
        } else {
          v.comment_pic_risk_result = {}
        }
      })
      adopt.value.list = adopt.value.list.concat(res.data.list)
    })
    .catch(v => {
      loading.value = false
    })
}
// 获取待审核数量
function getAdoptNum() {
  requests
    .getNumber({
      article_id: comment.value.id,
      state: 0
    })
    .then(res => {
      let Num = res.data.count
      if (Num > 99) {
        comment.value.adoptNumber = '99+'
      } else {
        comment.value.adoptNumber = Num
      }
      if (Num > 0) {
        comment.value.showTips = true
      } else {
        comment.value.showTips = false
      }
    })
}
// 判断是否为富文本
function isRichText(str) {
  return /<[a-z][\s\S]*>/i.test(str)
}
// 获取评论
function getComment(id) {
  if (id) {
    comment.value.id = id
  }
  let defaultId = comment.value.id
  loading.value = true
  comment.value.loadMore = true
  requests
    .getComment({
      sort_number: comment.value.sort_number,
      size: comment.value.size,
      article_id: comment.value.id
    })
    .then(res => {
      if (comment.value.id !== defaultId) return
      res.data.dtos.forEach(v => {
        if (v.sub_has_more) {
          v.showChild = false
        }
        if (v.comment_pic_risk_result) {
          v.comment_pic_risk_result = JSON.parse(v.comment_pic_risk_result)
        } else {
          v.comment_pic_risk_result = {}
        }
        if (v.sub_comment_list[0]) {
          v.sub_comment_list.forEach(k => {
            if (k.comment_pic_risk_result) {
              k.comment_pic_risk_result = JSON.parse(k.comment_pic_risk_result)
            } else {
              k.comment_pic_risk_result = {}
            }
          })
        }
      })
      comment.value.list = res.data.dtos
      loading.value = false
    })
    .catch(v => {
      loading.value = false
    })
}
// 获取热评
function getHot(id) {
  if (id) {
    comment.value.id = id
  }
  requests
    .getHotComment({
      article_id: comment.value.id
    })
    .then(res => {
      res.data.dtos.forEach(v => {
        if (v.sub_has_more) {
          v.showChild = false
        }
        if (v.comment_pic_risk_result) {
          v.comment_pic_risk_result = JSON.parse(v.comment_pic_risk_result)
        } else {
          v.comment_pic_risk_result = {}
        }
        if (v.sub_comment_list[0]) {
          v.sub_comment_list.forEach(k => {
            if (k.comment_pic_risk_result) {
              k.comment_pic_risk_result = JSON.parse(k.comment_pic_risk_result)
            } else {
              k.comment_pic_risk_result = {}
            }
          })
        }
      })
      comment.value.hotList = res.data.dtos
    })
}
// 热评展开更多
function getMoreHotComment(id) {
  requests
    .getMoreComment({
      article_id: comment.value.id,
      top_comment_id: id
    })
    .then(res => {
      res.data.commentFloorDTOList.forEach(v => {
        if (v.comment_pic_risk_result) {
          v.comment_pic_risk_result = JSON.parse(v.comment_pic_risk_result)
        } else {
          v.comment_pic_risk_result = {}
        }
      })
      comment.value.hotList.forEach(v => {
        if (v.id === id) {
          v.sub_comment_list = res.data.commentFloorDTOList
          v.showChild = true
        }
      })
    })
}
// 热评收起
function hotHide(id) {
  getHot()
  getComment()
  comment.value.hotList.forEach(v => {
    if (v.id === id) {
      v.sub_comment_list = [v.sub_comment_list[0]]
      v.showChild = false
    }
  })
}
// 普通评论展开更多
function getMoreComment(id) {
  requests
    .getMoreComment({
      article_id: comment.value.id,
      top_comment_id: id
    })
    .then(res => {
      res.data.commentFloorDTOList.forEach(v => {
        if (v.comment_pic_risk_result) {
          v.comment_pic_risk_result = JSON.parse(v.comment_pic_risk_result)
        } else {
          v.comment_pic_risk_result = {}
        }
      })
      comment.value.list.forEach(v => {
        if (v.id === id) {
          v.sub_comment_list = res.data.commentFloorDTOList
          v.showChild = true
        }
      })
    })
}
// 普通评论收起
function commendHide(id) {
  getHot()
  getComment()
  comment.value.list.forEach(v => {
    if (v.id === id) {
      v.sub_comment_list = [v.sub_comment_list[0]]
      v.showChild = false
    }
  })
}

// 置顶
function toTop(item) {}
// 设为热评
function setHot(item, act) {
  requests
    .setHot({
      action: act,
      comment_id: item.id,
      article_id: comment.value.id
    })
    .then(res => {
      getComment()
      getHot()
      ElMessage({
        type: 'success',
        message: '操作成功！'
      })
    })
}
// 点赞
function setLike(item) {
  console.log(item)
  requests.is_exists({ comment_id: item.id }).then(res => {
    if (res.code == 0) {
      if (res.data.exists == 0) {
        requests
          .like_save({
            comment_id: item.id,
            comment_user_id: item.account_id,
            comment_user_name: item.comment_user_name,
            article_id: comment.value.id,
            size: 1
          })
          .then(res2 => {
            if (res2.code === 0) {
              ElMessage({
                type: 'success',
                message: '操作成功！'
              })
            }
          })
      } else if (res.data.exists == 1) {
        ElMessage({
          type: 'info',
          message: '此条评论已点赞过！'
        })
      }
    }
  })
}
// 单条删除 type: 1热评 2普通评论  floor：1一级 2二级  index:二级评论一级层级 或父级obj
function handleDel(item, type, floor, index, index2) {
  ElMessageBox.confirm('删除后，可在“已删除”撤销', '确定删除该评论？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'Warning',
    icon: markRaw(Delete)
  })
    .then(() => {
      requests
        .setStete({
          status: 'DELETE',
          comment_ids: item.id,
          current_status: 'PENDING'
        })
        .then(res => {
          if (res.code === 0) {
            if (type === 1 && floor === 1) {
              comment.value.hotList = comment.value.hotList.filter(v => {
                return v.id !== item.id
              })
            }
            if (type === 1 && floor === 2) {
              if (index.sub_comment_list.length > 1) {
                getMoreHotComment(index.id)
              } else {
                getHot()
              }
            }
            if (type === 2 && floor === 1) {
              comment.value.list = comment.value.list.filter(v => {
                return v.id !== item.id
              })
            }
            if (type === 2 && floor === 2) {
              if (comment.value.list[index].sub_comment_list.length > 1) {
                getMoreComment(comment.value.list[index].id)
              } else {
                getComment()
              }

              // comment.value.list[index].sub_comment_list = comment.value.list[index].sub_comment_list.filter(v => { return v.id !== item.id })
              // comment.value.list[index].sub_count = comment.value.list[index].sub_count - 1
              // if (comment.value.list[index].sub_comment_list.length < 1) {
              //   comment.value.list[index].sub_has_more = false
              // }
            }
            if (item.status === 0) {
              setRightNumber(2)
            }
            ElMessage({
              type: 'success',
              message: '操作成功！'
            })
          }
        })
    })
    .catch(() => {})
}
// 通过接口 同上
function handlePass(item, type, floor, index, index2) {
  ElMessageBox.confirm('确定通过该评论？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'Warning',
    icon: markRaw(Select)
  })
    .then(() => {
      if (floor === 2) {
        if (type === 1 && index.status === 0) {
          ElMessage({
            type: 'error',
            message: '上级评论未通过，不可操作！'
          })
          return
        } else if (type === 2 && comment.value.list[index].status === 0) {
          ElMessage({
            type: 'error',
            message: '上级评论未通过，不可操作！'
          })
          return
        }
      }
      requests
        .setStete({
          status: 'PASS',
          comment_ids: item.id,
          current_status: 'PENDING'
        })
        .then(res => {
          if (res.code === 0) {
            if (type === 1 && floor === 2) {
              getMoreHotComment(index.id)
            }
            if (type === 2 && floor === 1) {
              getComment()
            }
            if (type === 2 && floor === 2) {
              getMoreComment(comment.value.list[index].id)
            }
            if (item.status === 0) {
              setRightNumber(1)
            }
            getAdoptNum()
            getLeftAdoptNum()
            ElMessage({
              type: 'success',
              message: '操作成功！'
            })
          }
        })
      ElMessage({
        type: 'success',
        message: '操作成功！'
      })
    })
    .catch(() => {})
}
// 待审核删除
function adoptDel(item) {
  ElMessageBox.confirm('删除后，可在“已删除”撤销', '确定删除该评论？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'Warning',
    icon: markRaw(Delete)
  })
    .then(() => {
      requests
        .setStete({
          status: 'DELETE',
          comment_ids: item.id,
          current_status: 'PENDING'
        })
        .then(res => {
          if (res.code === 0) {
            adopt.value.list = []
            adopt.value.current = 0
            getAdoptList()
            setRightNumber(2)
            getAdoptNum()
            ElMessage({
              type: 'success',
              message: '删除成功！'
            })
          }
        })
    })
    .catch(() => {})
}
// 待审核通过
function adoptPass(item) {
  ElMessageBox.confirm('确定通过该评论？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'Warning',
    icon: markRaw(Select)
  }).then(() => {
    requests
      .setStete({
        status: 'PASS',
        comment_ids: item.id,
        current_status: 'PENDING'
      })
      .then(res => {
        if (res.code === 0) {
          adopt.value.list = []
          adopt.value.current = 0
          getAdoptList()
          setRightNumber(1)
          getAdoptNum()
          getLeftAdoptNum()
          ElMessage({
            type: 'success',
            message: '操作成功！'
          })
        }
      })
      .catch(e => {
        console.log(e)
      })
  })
}
// 撤销至待审
function handleDelAdopt(item) {
  deleteToPass.value = false
  if (!commentIds.value.includes(item.id)) {
    commentIds.value.push(item.id)
  }
  requests.getDelType({ comment_id: item.id }).then(msg => {
    if (msg.data.is_exist) {
      ElMessage({
        type: 'error',
        message: '该评论已被违规删除，不可撤销！'
      })
    } else {
      requests
        .getParentCommentState({
          comment_level: item.comment_level,
          top_comment_id: item.top_comment_id,
          second_comment_id: item.second_comment_id,
          article_id: comment.value.id,
          state: 'CANCEL_TO_PENDING'
        })
        .then(res => {
          if (res.code == 0) {
            // comment 存在 上级评论在伤处列表
            if (res.data.result.commentList.length > 0) {
              //   设置弹窗
              disabledShow.value = true
              disableData.value = res.data.result.commentList
              res.data.result.commentList.forEach(v => {
                commentIds.value.push(v.id)
              })
            } else {
              disabledShow.value = false
              ElMessageBox.confirm('确定撤销该评论至待审核？', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'Warning',
                icon: markRaw(Delete),
                showClose: false
              }).then(() => {
                delRefrsh2(item.id, 1)
              })
            }
          }
        })
        .catch(() => {
          commentIds.value = []
        })
    }
  })
}
// 撤销删除全部刷新
function delRefrsh2(id, type) {
  requests
    .setStete({
      status: type === 1 ? 'CANCEL_TO_PENDING' : 'CANCEL_TO_PASS',
      comment_ids: commentIds.value.join(','),
      current_status: type === 1 ? 'PENDING' : 'DELETE'
    })
    .then(res => {
      if (res.code === 0) {
        del.value.current = 0
        del.value.list = []
        commentIds.value = []
        getDelList()
        getAdoptNum()
        ElMessage({
          type: 'success',
          message: '操作成功！'
        })
      }
    })
    .catch(() => {
      commentIds.value = []
    })
}
// 撤销并通过
function handleDelPass(item) {
  deleteToPass.value = true
  if (!commentIds.value.includes(item.id)) {
    commentIds.value.push(item.id)
  }
  requests.getDelType({ comment_id: item.id }).then(msg => {
    if (msg.data.is_exist) {
      ElMessage({
        type: 'error',
        message: '该评论已被违规删除，不可撤销！'
      })
    } else {
      requests
        .getParentCommentState({
          comment_level: item.comment_level,
          top_comment_id: item.top_comment_id,
          second_comment_id: item.second_comment_id,
          article_id: comment.value.id,
          state: 'CANCEL_TO_PASS'
        })
        .then(res => {
          if (res.code == 0) {
            // comment 存在 上级评论在伤处列表
            if (res.data.result.commentList.length > 0) {
              //   设置弹窗
              disabledShow.value = true
              disableData.value = res.data.result.commentList
              res.data.result.commentList.forEach(v => {
                commentIds.value.push(v.id)
              })
            } else {
              disabledShow.value = false
              ElMessageBox.confirm('确定撤销该评论并通过？', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'Warning',
                icon: markRaw(Delete)
              }).then(() => {
                delRefrsh2(item.id, 2)
              })
            }
          }
        })
        .catch(() => {
          commentIds.value = []
        })
    }
  })
}
// 右侧列表数字处理
function setRightNumber(type) {
  if (type === 1) {
    // 待审核通过
    articleList.value.forEach(v => {
      if (
        v.id === detail.value.id ||
        v.original_id === detail.value.id ||
        v.uuid === detail.value.id
      ) {
        v.auditing_count = v.auditing_count - 1
      }
    })
  } else {
    // 待审核删除
    articleList.value.forEach(v => {
      if (
        v.id === detail.value.id ||
        v.original_id === detail.value.id ||
        v.uuid === detail.value.id
      ) {
        v.auditing_count = v.auditing_count - 1
      }
    })
  }
}
// 时间转换
function handleTime2(time) {
  time = Number(time)
  var date = new Date(time)
  var Y = date.getFullYear() + '-'
  var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
  var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
  var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
  var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return Y + M + D + h + m + s
}
</script>
<style>
.circleMain a:-webkit-any-link {
  color: #0b82fd;
  text-decoration: none;
}
</style>
<style lang="scss" scoped>
img {
  width: 100%;
  height: 100%;
  display: block;
}

.circleMain {
  width: 100%;
  height: 100%;
  display: flex;
}

.cirle-list {
  width: 535px;
  border-radius: 12px;
  height: calc(100% - 20px);
  background: #fff;
  padding-top: 20px;
  margin-right: 10px;

  .head {
    padding-left: 15px;
    display: flex;

    .input-with-select2 {
      border-radius: 0px 6px 6px 0px;
      overflow: hidden;
      margin-left: 10px;
    }

    .tab {
      width: 123px;
      height: 32px;
      background: #ffffff;
      border-radius: 6px;
      border: 1px solid #dcdfe6;
      display: flex;
      justify-content: space-between;
      padding: 0 3px;
      align-items: center;
      margin-left: 25px;

      .child {
        cursor: pointer;
        width: 57px;
        height: 26px;
        font-size: 14px;
        text-align: center;
        line-height: 26px;
        color: #333333;
        position: relative;
      }

      .child.act {
        width: 57px;
        height: 26px;
        background: #eef5fe;
        border-radius: 3px;
        color: #0070f3;
      }

      .child.tip::after {
        content: ' ';
        width: 6px;
        height: 6px;
        background: #ff3333;
        border-radius: 50%;
        position: absolute;
        top: 2px;
        right: 2px;
      }
    }
  }

  .list {
    padding: 0 20px;
    height: calc(100% - 72px);
    width: 100%;
    overflow-y: scroll;
    margin-top: 20px;
    overflow-x: hidden;

    .child {
      width: 495px;
      height: 92px;
      border-radius: 6px;
      margin-bottom: 15px;
      background: rgba(210, 210, 210, 0.07);
      display: flex;
      padding: 6px 12px;
      cursor: pointer;

      .left {
        width: 60px;
        height: 80px;
        border-radius: 6px;
        margin-right: 10px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        border-radius: 6px;
        background: #f1f1f1;
        overflow: hidden;

        img {
          width: 100%;
          height: auto;
        }
      }

      .right {
        flex-shrink: 0;
        width: 410px;
        padding-right: 10px;

        .title {
          height: 40px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          overflow: hidden;
          -webkit-line-clamp: 2; // 控制要显示的行数
          font-size: 14px;
          color: #333333;
          line-height: 20px;
        }

        .footer {
          margin-top: 20px;
          display: flex;
          justify-content: space-between;

          .time {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
            line-height: 16px;
          }

          .nums {
            font-size: 14px;
            color: #333333;
            line-height: 16px;
            display: flex;
            align-items: center;

            .line {
              width: 1px;
              height: 8px;
              background: #666;
              margin: 0 10px;
            }

            .adoptNum,
            .commentNum {
              display: flex;
            }

            .commentNum::before {
              display: block;
              content: ' ';
              width: 15px;
              height: 15px;
              background: url('@/assets/images/circle/comment.png') no-repeat center center / 100%
                auto;
              margin-right: 4px;
            }

            .adoptNum::before {
              display: block;
              content: ' ';
              width: 15px;
              height: 15px;
              background: url('@/assets/images/circle/adopt.png') no-repeat center center / 100%
                auto;
              margin-right: 4px;
            }
          }
        }
      }
    }

    .child.act {
      background: rgba(12, 118, 243, 0.07);
    }
  }

  .list.null {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 400;
    font-size: 14px;
    color: #9faabd;
    flex-direction: column;

    .font {
      margin-top: 10px;
      text-align: center;
    }

    img {
      width: 150px;
      height: 150px;
    }
  }
}

.right-box {
  width: calc(100% - 555px);
  height: calc(100% - 20px);
  border-radius: 12px;
  overflow: hidden;
  background: #fff;
  display: flex;

  .comment-box {
    width: calc(100% - 390px);
    height: 100%;
    padding-top: 15px;
    position: relative;

    .tab {
      height: 32px;
      display: flex;
      font-size: 14px;
      line-height: 32px;
      flex-wrap: wrap;
      margin-left: 20px;

      .child {
        cursor: pointer;
        margin-right: 30px;
        position: relative;

        .num {
          font-size: 12px;
          padding: 2px 5px;
          background: #ff3333;
          text-align: center;
          color: #fff;
          position: absolute;
          left: 40px;
          top: -5px;
          border-radius: 8px;
          line-height: 12px;
        }
      }

      .child:last-child {
        margin-right: 0;
      }

      .child.act {
        color: #0b82fd;
        font-weight: 600;
        position: relative;

        .num {
          font-weight: 400;
        }
      }

      .child.act::after {
        content: ' ';
        width: 100%;
        position: absolute;
        flex-wrap: wrap;
        height: 4px;
        background: #0b82fd;
        border-radius: 2px;
        z-index: 10;
        left: 0;
        bottom: 0;
      }
    }

    .content {
      width: 100%;
      height: calc(100% - 45px);
      overflow-y: auto;
      padding: 0 20px 50px;

      .comment-list {
        margin-top: 15px;

        .child {
          margin-bottom: 20px;

          .head {
            display: flex;

            .avatar {
              width: 30px;
              height: 30px;
              border-radius: 50%;
              background: #ccc;
              overflow: hidden;
              margin-right: 6px;

              img {
                width: 100%;
                height: 100%;
              }
            }

            .right {
              display: flex;
              align-items: center;

              .name {
                font-size: 14px;
                font-weight: 400;
                color: #666666;
              }

              .guide {
                width: 36px;
                height: 20px;
                background: rgba(0, 112, 243, 0.11);
                border-radius: 3px;
                text-align: center;
                line-height: 20px;
                font-size: 12px;
                color: #0b82fd;
                margin-left: 6px;
              }
            }
          }

          .comment-detail {
            font-size: 14px;
            font-weight: 400;
            color: #000000;
            line-height: 24px;
            margin-top: 10px;
            word-wrap: break-word;

            .ifrBox {
              display: block;
              margin-top: 35px;
            }

            .reply-content {
              width: 100%;
              padding: 6px 10px;
              font-weight: 400;
              font-size: 12px;
              color: rgba(51, 51, 51, 0.8);
              background: #f5f5f5;
              line-height: 18px;
              margin-bottom: 10px;
              border-radius: 6px;
            }

            .pic {
              display: block;
              margin-top: 10px;
              width: 90px;
              height: auto;
            }

            .topping {
              display: inline-block;
              width: 36px;
              height: 14px;
              background: #ff3838;
              border-radius: 2px;
              line-height: 14px;
              font-weight: 400;
              font-size: 12px;
              color: #ffffff;
              text-align: center;
            }

            .hot {
              display: inline-block;
              width: 21px;
              height: 20px;
              margin-left: 6px;
              transform: translateY(5px);
            }
          }

          .footer {
            margin-top: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 400;
            font-size: 14px;
            color: #666666;

            .time {
              color: #acacac;

              span {
                margin-right: 6px;
              }
            }

            .f-right {
              flex-shrink: 0;
              display: flex;
            }

            .children {
              margin-right: 20px;
              display: flex;
              align-items: center;
              cursor: pointer;
            }

            .children:last-child {
              margin-right: 0;
            }

            .children:hover {
              filter: invert(44%) sepia(81%) saturate(4751%) hue-rotate(201deg) brightness(101%)
                contrast(108%);
            }

            .children.like2 {
              filter: invert(37%) sepia(78%) saturate(4902%) hue-rotate(347deg) brightness(95%)
                contrast(103%);
            }

            .children.pass {
              color: #0070f3;
            }

            .up::before {
              display: block;
              content: ' ';
              width: 14px;
              height: 14px;
              background: url('@/assets/images/circle/top.png') no-repeat center center / 100% auto;
              margin-right: 4px;
            }

            .up2::before {
              display: block;
              content: ' ';
              width: 14px;
              height: 14px;
              background: url('@/assets/images/circle/top2.png') no-repeat center center / 100% auto;
              margin-right: 4px;
            }

            .del2::before {
              display: block;
              content: ' ';
              width: 14px;
              height: 14px;
              background: url('@/assets/images/circle/del2.png') no-repeat center center / 100% auto;
              margin-right: 4px;
            }

            .pass::before {
              display: block;
              content: ' ';
              width: 14px;
              height: 14px;
              background: url('@/assets/images/circle/pass.png') no-repeat center center / 100% auto;
              margin-right: 4px;
            }

            .reply::before {
              display: block;
              content: ' ';
              width: 14px;
              height: 14px;
              background: url('@/assets/images/circle/reply.png') no-repeat center center / 100%
                auto;
              margin-right: 4px;
            }

            .like::before {
              display: block;
              content: ' ';
              width: 14px;
              height: 14px;
              background: url('@/assets/images/circle/like.png') no-repeat center center / 100% auto;
              margin-right: 4px;
            }

            .hot::before {
              display: block;
              content: ' ';
              width: 14px;
              height: 14px;
              background: url('@/assets/images/circle/hoter.png') no-repeat center center / 100%
                auto;
              margin-right: 4px;
            }

            .del::before {
              display: block;
              content: ' ';
              width: 14px;
              height: 14px;
              background: url('@/assets/images/circle/del.png') no-repeat center center / 100% auto;
              margin-right: 4px;
            }
          }

          .reply-list {
            margin-top: 15px;
            padding: 15px;
            background: #f7fbff;
            border-radius: 6px;

            .reply-child {
              margin-bottom: 15px;

              .reply-head {
                display: flex;
                align-items: center;

                .reply-avatar {
                  width: 30px;
                  height: 30px;
                  border-radius: 50%;
                  background: #ccc;
                  overflow: hidden;
                  margin-right: 6px;

                  img {
                    width: 100%;
                    height: 100%;
                  }
                }

                .reply-right {
                  .reply-name {
                    font-size: 14px;
                    font-weight: 400;
                    color: #666666;
                    display: flex;
                    align-items: center;
                  }

                  .reply-avatar {
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                    background: #ccc;
                    overflow: hidden;
                    margin-right: 6px;

                    img {
                      width: 100%;
                      height: 100%;
                    }
                  }

                  .guide {
                    width: 36px;
                    height: 20px;
                    background: rgba(0, 112, 243, 0.11);
                    border-radius: 3px;
                    text-align: center;
                    line-height: 20px;
                    font-size: 12px;
                    color: #0b82fd;
                    margin-left: 6px;
                  }
                }
              }

              .reply-detail {
                margin-top: 10px;
                font-size: 14px;
                font-weight: 400;
                color: #000000;
                line-height: 24px;
                word-wrap: break-word;

                .pic {
                  display: block;
                  margin-top: 10px;
                  width: 90px;
                  height: auto;
                }
              }

              .footer {
                margin-top: 10px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-weight: 400;
                font-size: 14px;
                color: #666666;

                .time {
                  color: #acacac;

                  span {
                    margin-right: 6px;
                  }
                }

                .f-right {
                  flex-shrink: 0;
                  display: flex;
                }

                .children {
                  margin-right: 20px;
                  display: flex;
                  align-items: center;
                  cursor: pointer;
                }

                .children:last-child {
                  margin-right: 0;
                }

                .children:hover {
                  filter: invert(44%) sepia(81%) saturate(4751%) hue-rotate(201deg) brightness(101%)
                    contrast(108%);
                }

                .children:last-child {
                  margin-right: 0;
                }

                .children.like2 {
                  filter: invert(37%) sepia(78%) saturate(4902%) hue-rotate(347deg) brightness(95%)
                    contrast(103%);
                }

                .reply::before {
                  display: block;
                  content: ' ';
                  width: 14px;
                  height: 14px;
                  background: url('@/assets/images/circle/reply.png') no-repeat center center / 100%
                    auto;
                  margin-right: 4px;
                }

                .like::before {
                  display: block;
                  content: ' ';
                  width: 14px;
                  height: 14px;
                  background: url('@/assets/images/circle/like.png') no-repeat center center / 100%
                    auto;
                  margin-right: 4px;
                }

                .del::before {
                  display: block;
                  content: ' ';
                  width: 14px;
                  height: 14px;
                  background: url('@/assets/images/circle/del.png') no-repeat center center / 100%
                    auto;
                  margin-right: 4px;
                }
              }
            }

            .reply-child:last-child {
              margin-bottom: 0;
            }
          }
        }
      }

      .comment-list.null {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #9faabd;
        flex-direction: column;

        .font {
          margin-top: 10px;
        }

        img {
          width: 150px;
          height: 150px;
        }
      }

      .show-more {
        display: flex;
        align-items: center;
        font-size: 12px;
        font-weight: 400;
        color: #0b82fd;
        margin-top: 10px;
        cursor: pointer;

        .line {
          width: 40px;
          height: 1px;
          border-top: 1px solid #0b82fd;
          margin-right: 10px;
        }

        .svg-icon {
          width: 14px;
          height: 14px;
          margin-left: 2px;
        }
      }
    }
  }
}
</style>
<style>
.input-with-select2 .el-input-group__append {
  background-color: #0070f3;
  color: #fff;
  border-radius: 0px 6px 6px 0px;
}

.text.circle {
  img {
    width: 350px;
    height: auto;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 6px;
  }

  iframe {
    width: 350px;
    height: 615px;
  }

  video {
    width: 360px;
    height: 467px;
  }
}

.comment-box.inCircle {
  .el-divider--horizontal {
    margin: 15px 0;
  }
}
</style>

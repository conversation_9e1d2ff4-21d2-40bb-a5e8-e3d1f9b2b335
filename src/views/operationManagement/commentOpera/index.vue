<template>
  <AbsoluteContainer v-if="!showSubject">
    <page-main v-show="!showTask && !showSort && !showSubject">
      <headModel @openTask="openTask" @search="handleSearch" />
      <div id="tabel-component">
        <el-table v-loading="loading" :data="data.dataList" size="default" :height="data.height">
          <el-table-column v-tab="'comment_opera_article_id'" v-slot="scope" label="稿件ID" width="60" :show-overflow-tooltip="true">
            {{ scope.row.uuid ? scope.row.uuid : scope.row.id }}
          </el-table-column>
          <el-table-column v-tab="'comment_opera_article_title'" v-slot="scope" min-width="400" label="稿件标题" :show-overflow-tooltip="true" :tooltip-options="{
            popperOptions:{
              width: '30vh'
            }
          }">
            <a v-if="scope.row.list_title" class="elemLink" target="_blank" :href="scope.row.url">{{
              scope.row.list_title
            }}</a>
            <a v-else class="elemLink" target="_blank" :href="scope.row.url">无标题</a>
          </el-table-column>
          <!-- <el-table-column prop="doc_type_name" label="稿件类型" width="100"  /> -->
          <!-- <el-table-column prop="channel_name" label="来源频道" width="120"  /> -->
          <el-table-column v-tab="'comment_opera_publish_time'" prop="published_at" label="发布时间" width="180" />
          <!-- <el-table-column prop="activityCommentCount" label="组件数" width="100"  /> -->
          <el-table-column v-tab="'comment_opera_read_num'" prop="read_total" label="稿件阅读数" width="120" />

          <el-table-column v-tab="'comment_opera_pass_num'" prop="passed_comment_count" label="已通过" width="60" />
          <el-table-column v-tab="'comment_opera_publish_num'" prop="published_comment_count" label="已发布" width="60" />
          <el-table-column v-tab="'comment_opera_wait_num'" prop="wait_comment_count" label="待发布" width="60" />
          <el-table-column v-tab="'comment_opera_comment_count'" prop="total_comment_count" label="" width="120">
            <template #header>
              评论总数 <el-tooltip
                class="box-item"
                effect="dark"
                content="评论总数=已通过的评论数+已发布的评论数+待发布的评论数。已发布评论数(已发布评论数、待发布评仑数为后台马甲号发布的评论)"
                placement="top"
              >
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column v-tab="'comment_opera_operate'" v-slot="scope" label="操作" width="330" fixed="right">
            <el-button
              v-if="scope.row.doc_type_name !== '专题'"
              v-auth="'comment_operation_task:save'"
              style="margin-left: 0; padding: 5px 5px"
              text
              size="small"
              @click="handleAdd(scope.row)"
            >
              <span style="color: #0b82fd">创建任务</span>
            </el-button>
            <el-button
              v-if="scope.row.doc_type_name !== '专题'"
              v-auth="'comment:batch_list'"
              text
              size="small"
              style="margin-left: 0; padding: 5px 5px"
              @click="handleAll(scope.row)"
            >
              <span style="color: #0b82fd">赞/评/删</span>
            </el-button>
            <el-button
              v-if="scope.row.doc_type_name !== '专题'"
              v-auth="'comment_sort:list'"
              text
              size="small"
              style="margin-left: 0; padding: 5px 5px"
              @click="toSort(scope.row)"
            >
              <span style="color: #0b82fd">评论排序</span>
            </el-button>

            <el-button
              v-if="scope.row.doc_type_name === '专题'"
              text
              size="small"
              style="margin-left: 0; padding: 5px 5px"
              @click="handleShowSubject(scope.row)"
            >
              <span style="color: #0b82fd">查看稿件</span>
            </el-button>
            <el-button
              v-auth="'comment_operation_task:list'"
              text
              size="small"
              style="margin-left: 0; padding: 5px 5px"
              @click="openTask(scope.row)"
            >
              <span style="color: #0b82fd">查看任务</span>
            </el-button>
            <el-button
              v-auth="'article_operator_log:list'"
              text
              size="small"
              style="margin-left: 0; padding: 5px 5px"
              @click="showHistory(scope.row)"
            >
              <span style="color: #0b82fd">操作日志</span>
            </el-button>
            <!-- <el-popover placement="bottom"
                      :width="85"
                      trigger="hover">
            <template #reference>
              <el-button v-if="scope.row.doc_type_name !== '专题'"
                         text
                         size="small"
                         style="margin-left: 0; padding: 5px 5px;">
                <span style="color:#0B82FD;">更多</span>
              </el-button>
            </template>
            <template #default>
              <el-button v-auth="'comment_operation_task:list'"
                         text
                         size="small"
                         style="margin-left: 0; padding: 5px 5px;"
                         @click="openTask(scope.row)">
                <span style="color:#0B82FD;">查看任务</span>
              </el-button>
              <el-button v-auth="'article_operator_log:list'"
                         text
                         size="small"
                         style="margin-left: 0; padding: 5px 5px;"
                         @click="showHistory(scope.row)">
                <span style="color:#0B82FD;">操作日志</span>
              </el-button>
            </template>
          </el-popover> -->
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页 -->
      <div class="page">
        <el-pagination
          background
          small
          layout="total, sizes, prev, pager, next, jumper"
          :total="data.total"
          :page-size="data.size"
          :current-page="data.current"
          :page-sizes="[10, 20, 50]"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </page-main>
    <page-main v-show="showTask && !showSort">
      <CommentTask ref="commentTask" @back="back" />
    </page-main>
    <page-main v-show="showSort && !showTask">
      <Sort ref="sort" @back="back" />
    </page-main>

    <AllComment ref="allComment" />
    <History ref="history" />
    <Add ref="add" />
  </AbsoluteContainer>
  <AbsoluteContainer v-else>
    <Subject ref="subject" :subject="subjectMsg" @back="back" />
  </AbsoluteContainer>

</template>

<script setup name="cardList">
import { requests } from '@/api/business/commentOpera'
import { nextTick, ref } from 'vue'
import CommentTask from './commentTask.vue'
import Add from './modules/add.vue'
import AllComment from './modules/allComment.vue'
import headModel from './modules/headModule.vue'
import History from './modules/operateHistory.vue'
import Sort from './sort.vue'
import Subject from './specialSubject.vue'

const allComment = ref(null)
const route = useRoute()
const add = ref(null)
const showTask = ref(false)
const showSort = ref(false)
const data = ref({
  height: null,
  dataList: [],
  total: 0,
  size: 50,
  current: 1,
  channel_id: '',
  article_title: '',
  article_id: '',
  circle_id: ''
})
const subjuect = ref(null)
const showSubject = ref(false)
const subjectMsg = ref({})
const commentTask = ref(null)
const sort = ref(null)
const loading = ref(false)
const history = ref(null)
const timeStamp = ref()
onMounted(() => {
  nextTick(() => {
    // 获取表头高度，然后设置 .el-table__body-wrapper 的 height
    let height = document.getElementById('tabel-component').offsetHeight
    let bottom = document.getElementsByClassName('el-pagination')[0].offsetHeight
    data.value.height = height - bottom
  })
  // 获取地址栏ID
  if (route.query.id) {
    data.value.article_id = route.query.id
    let searchData = {
      size: data.value.size,
      current: data.value.current,
      channel_id: data.value.channel_id,
      article_title: data.value.article_title,
      article_id: data.value.article_id,
      circle_id: data.value.circle_id
    }
    loading.value = true
    requests
      .getList(searchData)
      .then(res => {
        loading.value = false
        data.value.total = res.data.release_list.total > 10000 ? 10000 : res.data.release_list.total
        data.value.dataList = res.data.release_list.records
        handleAll(res.data.release_list.records[0])
      })
      .catch(() => {
        loading.value = false
      })
  } else {
    init()
  }
})
// 初始化
function init() {
  let searchData = {
    size: data.value.size,
    current: data.value.current,
    channel_id: data.value.channel_id,
    article_title: data.value.article_title,
    article_id: data.value.article_id,
    circle_id: data.value.circle_id
  }
  loading.value = true
  requests
    .getList(searchData)
    .then(res => {
      loading.value = false
      data.value.total = res.data.release_list.total > 10000 ? 10000 : res.data.release_list.total
      data.value.dataList = res.data.release_list.records
    })
    .catch(() => {
      loading.value = false
    })
}
// 展示专题稿件内容
function handleShowSubject(val) {
  console.log(val)
  subjectMsg.value = val
  showSubject.value = true
}
// 搜索
function handleSearch(val) {
  data.value.channel_id = val.channel_id
  data.value.article_title = val.article_title
  data.value.article_id = val.article_id
  data.value.circle_id = val.circle_id
  init()
}
// 操作日志
function showHistory(val) {
  history.value.drawer = true
  history.value.info = val
  history.value.init()
}
// 查看所有评论
function handleAll(row) {
  timeStamp.value = new Date().getTime()
  let timing = timeStamp.value
  // 马甲号列表
  requests.vest_list({ size: 10 }).then(res => {
    if (res.code == 0) {
      allComment.value.vestList = res.data.vest_list
      if (allComment.value.vestList.length > 0) {
        for (let i = 0; i < allComment.value.vestList.length; i++) {
          allComment.value.vestList[i].isClick = false
        }
        // 默认第一个马甲
        const vestOne = allComment.value.vestList[0]
        vestOne.isClick = true
        allComment.value.dataVal.comment_user_id = vestOne.id
        allComment.value.dataVal.comment_user_name = vestOne.nick_name
        allComment.value.dataVal.comment_user_portrait = vestOne.image_url
        allComment.value.dataVal.comment_user_location = vestOne.location
      }
    }
  })
  let newData = Object.assign({}, row)
  let article_id = ''
  if (newData.uuid) {
    article_id = newData.uuid
  } else if (newData.ugc_uuid) {
    article_id = newData.ugc_uuid
  } else if (newData.original_id) {
    article_id = newData.original_id
  } else {
    article_id = newData.id
  }
  if (article_id) {
    // 总评论数
    // 使用 Promise.all 并行请求数据
    Promise.all([
      requests.count({ article_id }),
      requests.floor_list({ article_id, sort_number: 0, size: 10 }),
      requests.hot_comments({ article_id })
    ]).then(([countRes, floorRes, hotRes]) => {
      if (timing !== timeStamp.value) {
        return
      }
      // 处理评论总数
      if (countRes.code === 0) {
        allComment.value.dataVal.countNum = countRes.data.count
      }

      // 处理评论列表
      if (floorRes.code === 0) {
        allComment.value.drawer = true
        allComment.value.moreNullData = false
        allComment.value.comment_level = floorRes.data.dtos

        // 设置文章相关信息
        const { dataVal } = allComment.value
        dataVal.article_id = article_id
        dataVal.list_pics = newData.list_pics[0]
        dataVal.article_title = newData.list_title
        dataVal.channel_name = newData.channel_name
        dataVal.article_url = newData.url
        dataVal.article_published_at = newData.published_at
        dataVal.article_user_id = newData.account_id || ''
        dataVal.channel_id = newData.channel_id
      }

      // 处理热门评论
      if (hotRes.code === 0) {
        const hotData = hotRes.data.dtos.map(item => ({
          ...item,
          hotCommentsFlag: true
        }))
        allComment.value.comment_level = [...hotData, ...allComment.value.comment_level]
      }
    }).catch(error => {
      console.error('获取评论数据失败:', error)
    })
  }
}
// 创建评论任务
function handleAdd(row) {
  add.value.ruleForm.isEdit = false
  add.value.batchTasks = false
  // 马甲号列表
  requests.vest_list({ size: 10 }).then(res => {
    if (res.code == 0) {
      add.value.drawer = true
      add.value.gridData = []

      let newData = Object.assign({}, row)
      add.value.ruleForm.list_pics = newData.list_pics[0]
      add.value.ruleForm.article_title = newData.list_title
      add.value.ruleForm.channel_id = newData.channel_id
      add.value.ruleForm.channel_name = newData.channel_name
      add.value.ruleForm.article_url = newData.url
      add.value.ruleForm.article_published_at = newData.published_at
      newData.account_id
        ? (add.value.ruleForm.article_user_id = newData.account_id)
        : (add.value.ruleForm.article_user_id = '')

      if (newData.uuid) {
        add.value.ruleForm.article_id = newData.uuid
        console.log('uuid')
      } else if (newData.ugc_uuid) {
        add.value.ruleForm.article_id = newData.ugc_uuid
        console.log('ugc_uuid')
      } else if (newData.original_id) {
        add.value.ruleForm.article_id = newData.original_id
        console.log('original_id')
      } else {
        add.value.ruleForm.article_id = newData.id
        console.log('id')
      }
    }
  })
}
// 打开全部任务
function openTask(value) {
  showTask.value = true
  if (value) {
    let id = value.original_id ? value.original_id : value.id
    if (value.uuid || value.ugc_uuid) {
      id = value.uuid || value.ugc_uuid
    }
    commentTask.value.searchTitle(id)
    commentTask.value.getStateNum(id)
  } else {
    commentTask.value.getStateNum()
    commentTask.value.init()
  }
}
// 评论排序
function toSort(val) {
  showSort.value = true
  sort.value.Info = val
  sort.value.init()
}
function back() {
  showTask.value = false
  showSort.value = false
  showSubject.value = false
}
// 选择每页几条
const handleSizeChange = val => {
  data.value.size = val
  data.value.current = 1
  init()
}
// 点击分页器
const handleCurrentChange = val => {
  data.value.current = val
  init()
}
</script>

<style lang="scss" scoped>
#tabel-component {
  height: 100%;
}
.elemLink {
  color: #606266;
  text-decoration: none;
}

a {
  text-decoration: none;
  color: #333;
}

a:hover,
a:visited,
a:link,
a:active {
  color: #333;
}

.page {
  display: flex;
  justify-content: center;
  height: 45px;
}

.page-main {
  display: flex;
  flex-direction: column;

  // 减去的 40px 为 page-main 的上下 margin
  // 减去的 130px 为 page-header 的高度，如果没有设置，可以去掉
  :deep(.el-table) {
    height: 100%;

    .el-table__body-wrapper {
      overflow-y: auto;
    }
  }
}
</style>

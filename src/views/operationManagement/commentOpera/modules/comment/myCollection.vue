<template>
<div :class="isCircle ? 'myCollectionStyle' : ''">
<el-form-item class="vestList">
    <div
        v-for="item of vestList"
        v-show="vestList.length"
        :key="item.id"
        :class="item.isClick ? 'imgListClick': 'imgList'"
        @click="handleVestClick(item.image_url,item.nick_name,item.location,item.id)"
    >
        <img :src="item.image_url" />
        <div class="toping" @click.stop="handleDelect(item.auto_pk)"></div>
        <div class="sub-title">
            <span>{{ item.nick_name }}</span>
            <p>{{ item.location.split(',')[1] + item.location.split(',')[2] }}</p>
        </div>
    </div>
    <div v-show="vestList.length < 10" class="imgList">
        <div class="add" @click="handleAdd">+添加</div>
    </div>  
</el-form-item> 
  </div>
  <el-dialog
    v-model="drawer"
    direction="rtl"
    title="添加马甲号"
    width="630"
    close-on-click-moda="false"
  >
    <div class="search">
      <span>
        昵称
      </span>
      <el-input
        v-model="nick_name"
        style="width: 200px;margin:0 10px"
        placeholder="请输入昵称"
      />
      <el-button
        type="primary"
        style="width: 80px;"
        @click="getVest"
      >
        搜索
      </el-button>
    </div>
    <div class="number-box">
      <div v-for="item of list" :key="item.id" :class="item.id === act ? 'child act':'child'" @click="checkVest(item)">
        <img :src="item.image_url" />
        <div class="sub-title">
          <span>{{ item.nick_name }}</span>
          <p>{{ item.location.split(',')[1] + item.location.split(',')[2] }}</p>
        </div>
      </div>
    </div>
    <template #footer>
      <div style="flex: auto;">
        <el-button @click="drawer = false">取消</el-button>
        <el-button
          type="primary"
          @click="sureClick"
        >
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup name="myCollection">
import { ref, defineEmits, defineProps } from 'vue'
import { requests } from '@/api/business/commentOpera'
import { ElMessage, ElMessageBox } from 'element-plus'
const emit = defineEmits(['selectMyCollection', 'delMyCollection'])
const props = defineProps({
  isCircle: {
    type: Boolean
  }
})
const vestList = ref([])
const commentInfo = ref({
  comment_user_id: '',
  comment_user_name: '',
  comment_user_portrait: '',
  comment_user_location: ''
})
const drawer = ref(false)
const nick_name = ref('')
const list = ref([])
const act = ref('')
onMounted(() => {
  vest_collect_list()
})
function getVest() {
  act.value = ''
  if (nick_name.value) {
    requests.vest_list({ nick_name: nick_name.value, size: 500 }).then(res => {
      if (res.data.vest_list.length > 0) {
        list.value = res.data.vest_list
      } else {
        list.value = []
        ElMessage.error('暂无搜索结果')
      }
      
    })
  } else {
    ElMessage({
      message: '请输入马甲号昵称',
      type: 'error'
    })
  }
}
function checkVest(val) {
  act.value = val.id
}
// 添加
const handleAdd = () => {
  drawer.value = true
  nick_name.value = ''
  list.value = []
  // getVest()
}
// 确认
function sureClick() {
  if (act.value === '') {
    ElMessage.error('请选择要收藏的马甲号')
    return
  } else {
    requests.vest_collect_add({ id: act.value }).then(res => {
      if (res.code == 0) {
        drawer.value = false
        vest_collect_list()
        ElMessage({
          message: '收藏马甲号成功',
          type: 'success'
        })
        act.value  = ''
      } else {
        ElMessage({
          message: '收藏马甲号失败',
          type: 'error'
        })
      }
    })
  }
}
// 删除收藏号
const handleDelect = auto_pk => {
  requests.vest_collect_delete({ auto_pk }).then(res => {
    if (res.code == 0) {
      ElMessage({
        message: '删除成功',
        type: 'success'
      })
      emit('delMyCollection')
      vest_collect_list()
    } else {
      ElMessage({
        message: '删除失败',
        type: 'error'
      })
    }
  })
}
// 收藏列表
const vest_collect_list = () => {
  const data = {
    nick_name: '',
    location: '',
    size: '10'
  }
  requests.vest_collect_list(data).then(res => {
    if (res.code == 0) {
      vestList.value = res.data.vest_list_collect
    }
  })
}
// 点击选中马甲号
const handleVestClick = (image_url, nick_name, location, id) => {
  emit('selectMyCollection')
  commentInfo.value.comment_user_id = id
  commentInfo.value.comment_user_name = nick_name
  commentInfo.value.comment_user_portrait = image_url
  commentInfo.value.comment_user_location = location

  vestList.value.forEach((v, index) => {
    if (v.id == id) {
      vestList.value[index].isClick = true
    } else {
      vestList.value[index].isClick = false
    }
  })
  
}
defineExpose({
  drawer,
  commentInfo,
  vestList
})
</script>
<style lang="scss" scoped>
.myCollectionStyle{
  margin-top: -5px;
  margin-left: -35px;
  transform: scale(.9);
}
.vestList{
  width: 765px;
  margin-top: 20px;
  max-height: 160px;
  overflow: hidden;
}
.imgList,
  .imgListClick {
    width: 135px;
    height: 65px;
    padding-top: 5px;
    float: left;
    margin: 0 18px 20px 0;
    cursor: pointer;
    background-color: #f2f2f2;
    position: relative;
    border-radius: 5px;
    img {
      width: 20px;
      height: 20px;
      margin: 5px 5px 0 5px;
      border-radius: 50%;
      object-fit: cover;
      flex: 1;
      float: left;
    }
    .toping{
     display: block;
     width: 15px;
     height: 14px;
     background: url('@/assets/images/commentDel.png') no-repeat;
     background-size: 15px auto;
    position: absolute;
    right: 0;
    top: 0;
    display: none;
    }
    .add{
        text-align: center;
        line-height: 50px;
    }
    .sub-title {
      float: left;
      margin-top: 5px;
      span {
        display: block;
        height: 25px;
        width: 100px;
        line-height: 20px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 12px;
      }
      p {
        width: 100px;
        margin-left: -23px;
        color: rgb(11, 130, 253);
        font-size: 13px;
        margin-top: -5px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    &:hover {
      border: 1px dashed #0B82FD;
       .toping{
         display: block;
      }
    }
  }
  .imgListClick {
    background-color: #deebfe;
    border-radius: 5px;
  }
.search {
  display: flex;
  align-items: center;
}
.number-box {
  margin-top: 20px;
  max-height: 320px;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  .child.act{
    background-color: #deebfe;
  }
  .child {
    width: 135px;
    height: 60px;
    padding-top: 10px;
    margin: 0 10px 20px 0;
    background-color: #f2f2f2;
    border-radius: 5px;
    display: flex;
    position: relative;
    cursor: pointer;
    &:hover {
        border: 1px dashed #0B82FD;
    }
    img {
      width: 20px;
      height: 20px;
      margin-left: 5px;
      margin-right: 5px;
      border-radius: 50%;
      object-fit: cover;
    }
    .sub-title {
      width: 100px;
      span {
        display: block;
        height: 25px;
        width: 100px;
        line-height: 20px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 12px;
      }
      p {
        width: 100px;
        margin-left: -25px;
        color: rgb(11, 130, 253);
        font-size: 13px;
        margin-top: -1px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
